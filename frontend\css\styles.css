/* 全局樣式 - 基於 Reference/index.html 的響應式設計 */

/* 基礎設置 */
html, body {
    height: 100%;
    overflow: hidden; /* 全局禁用滾動 */
}

body {
    font-family: 'Inter', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 使用 dvh 單位確保在手機上高度正確 */
    height: 100dvh; 
}

/* 練習反饋樣式 */
.correct-answer {
    background-color: #dcfce7 !important; /* green-100 */
    border-color: #4ade80 !important; /* green-400 */
}

.wrong-answer {
    background-color: #fee2e2 !important; /* red-100 */
    border-color: #f87171 !important; /* red-400 */
}

/* 按鈕禁用時的樣式 */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 為內部滾動區域設計一個細微的滾動條 */
.subtle-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.subtle-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.subtle-scrollbar::-webkit-scrollbar-thumb {
    background-color: #cbd5e1; /* slate-300 */
    border-radius: 20px;
}

/* 進度條樣式 */
.progress-bar {
    background-color: #f1f5f9;
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    height: 100%;
    border-radius: 9999px;
    transition: width 0.3s ease;
}

/* 狀態標籤樣式 */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-assigned {
    background-color: #fef3c7;
    color: #92400e;
}

.status-learning {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-mastered {
    background-color: #dcfce7;
    color: #166534;
}

.status-review {
    background-color: #fce7f3;
    color: #be185d;
}

/* 動畫效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 載入動畫 */
.loading-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隱藏/顯示工具類 */
.hidden {
    display: none !important;
}

.show {
    display: block !important;
}

.flex-show {
    display: flex !important;
}

/* 觸控優化 */
@media (hover: none) and (pointer: coarse) {
    .hover\:scale-105:hover {
        transform: none;
    }
    
    .hover\:bg-slate-100:hover {
        background-color: inherit;
    }
    
    button:active {
        transform: scale(0.98);
    }
}

/* 焦點樣式 */
.focus-ring:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 錯誤樣式 */
.error-message {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.input-error {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* 成功樣式 */
.success-message {
    color: #059669;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.input-success {
    border-color: #059669 !important;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}
