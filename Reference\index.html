<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>香港初中生字卡記憶工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        html, body {
            height: 100%;
            overflow: hidden; /* 全局禁用滾動 */
        }
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            /* 使用 dvh 單位確保在手機上高度正確 */
            height: 100dvh; 
        }
        .correct-answer {
            background-color: #dcfce7 !important; /* green-100 */
            border-color: #4ade80 !important; /* green-400 */
        }
        .wrong-answer {
            background-color: #fee2e2 !important; /* red-100 */
            border-color: #f87171 !important; /* red-400 */
        }
        /* 按鈕禁用時的樣式 */
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        /* 為內部滾動區域設計一個細微的滾動條 */
        .subtle-scrollbar::-webkit-scrollbar {
            width: 4px;
        }
        .subtle-scrollbar::-webkit-scrollbar-track {
            background: transparent;
        }
        .subtle-scrollbar::-webkit-scrollbar-thumb {
            background-color: #cbd5e1; /* slate-300 */
            border-radius: 20px;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800 flex flex-col p-4">

    <div id="app-container" class="w-full max-w-md mx-auto flex flex-col flex-grow justify-center h-full">

        <!-- Start Screen -->
        <div id="start-screen" class="text-center">
            <h1 class="text-3xl sm:text-4xl font-bold mb-2 text-slate-900">生字卡記憶工具</h1>
            <p class="text-slate-600 mb-8">專為香港初中學生設計</p>
            <div class="space-y-4">
                <button onclick="startApp('S1')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中一 (S1) 詞彙
                </button>
                <button onclick="startApp('S2')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中二 (S2) 詞彙
                </button>
                <button onclick="startApp('S3')" class="w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    中三 (S3) 詞彙
                </button>
                <button onclick="startApp('TEST')" class="mt-2 w-full bg-yellow-100 border border-yellow-300 hover:bg-yellow-200 text-yellow-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105">
                    壓力測試 (長文本)
                </button>
            </div>
        </div>

        <!-- Main App Screen (Study & Practice) -->
        <div id="main-app" class="hidden flex flex-col flex-grow h-full">
            <!-- Header -->
            <div class="flex-shrink-0 flex justify-between items-center mb-4">
                <button onclick="showStartScreen()" class="text-slate-500 hover:text-slate-800">&larr; 返回主頁</button>
                <div class="text-sm font-semibold">
                    <span id="grade-display" class="bg-blue-100 text-blue-800 px-2 py-1 rounded"></span>
                    <span id="progress-display" class="bg-slate-200 px-2 py-1 rounded"></span>
                </div>
            </div>

            <!-- Main Content Area: This container will manage the layout -->
            <div class="flex-grow flex flex-col min-h-0 justify-center">
                <!-- Study View -->
                <div id="study-view" class="flex flex-col justify-center min-h-0">
                    <div id="flashcard" class="w-full bg-white rounded-xl shadow-lg border border-slate-200 p-4 sm:p-6 flex flex-col min-h-0 mb-4">
                        <div class="flex-shrink-0 flex items-start justify-between">
                            <div class="flex-grow min-w-0 mr-2">
                                <h2 id="card-word" class="text-3xl sm:text-4xl font-bold text-slate-900 break-all whitespace-nowrap"></h2>
                                <p id="card-phonetic" class="text-base sm:text-lg text-slate-500 mt-1 break-words"></p>
                            </div>
                            <button onclick="speakWord()" class="flex-shrink-0 p-2 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-colors">
                                <svg class="w-6 h-6 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Responsive Layout: flex-col on mobile, sm:flex-row on desktop -->
                        <div class="flex-grow flex flex-col sm:flex-row items-center min-h-0 mt-3">
                            <!-- Image Column -->
                            <div id="card-image-container" class="w-full sm:w-1/4 h-16 sm:h-full flex-shrink-0 flex items-center justify-center">
                            </div>
                            <!-- Text Content Column with internal scrolling -->
                            <div class="w-full sm:w-3/4 flex flex-col justify-between h-full sm:pl-4 mt-3 sm:mt-0 text-sm overflow-y-auto subtle-scrollbar">
                                <div class="space-y-2">
                                    <div>
                                        <p class="font-semibold text-slate-900">中文解釋:</p>
                                        <p id="card-translation-zh" class="text-slate-700"></p>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-slate-900">英文解釋:</p>
                                        <p id="card-definition-en" class="text-slate-700"></p>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-slate-900">例句:</p>
                                        <p id="card-sentence-en" class="text-slate-700"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <!-- Controls are now guaranteed to be visible -->
                    <div class="flex-shrink-0">
                        <div class="grid grid-cols-2 gap-3">
                            <button id="prev-btn" onclick="previousCard()" class="w-full bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-semibold py-3 rounded-lg shadow-sm"> &larr; 上一個</button>
                            <button id="next-btn" onclick="nextCard()" class="w-full bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-semibold py-3 rounded-lg shadow-sm">下一個 &rarr;</button>
                        </div>
                         <button onclick="startPractice()" class="mt-4 w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 rounded-lg shadow-sm">開始練習！</button>
                    </div>
                </div>
                
                <!-- Practice View -->
                <div id="practice-view" class="hidden flex flex-col flex-grow justify-center">
                     <p id="practice-instruction" class="text-center text-slate-600 mb-2 font-semibold"></p>
                     <div id="practice-question" class="text-center text-slate-800 mb-4 text-lg"></div>
                     <div id="practice-content" class="space-y-3">
                         <!-- Practice content (options or input) will be generated by JS -->
                     </div>
                </div>

                <!-- Results View -->
                <div id="results-view" class="hidden text-center">
                     <h2 class="text-2xl font-bold mb-4">練習完成！</h2>
                     <p class="text-lg mb-2">你的成績是:</p>
                     <p id="score-display" class="text-5xl font-bold text-blue-600 mb-8"></p>
                     <button onclick="startApp(appState.currentGrade)" class="w-full bg-slate-800 hover:bg-slate-900 text-white font-bold py-3 rounded-lg shadow-sm">再次學習</button>
                </div>
            </div>
        </div>
    </div>

<script>
// --- DATA SOURCE ---
const wordDatabase = {
    S1: [
        { id: 's1_01', text: 'festival', phonetic: '/ˈfes.tɪ.vəl/', grade: 'S1', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🎉%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '節日' },
          definitions: { en: 'A special day or period, usually in memory of a religious event, with its own social activities, food, or ceremonies.' },
          sentences: { en: 'Chinese New Year is the most important festival for Chinese people, celebrated with dragon dances and family gatherings.' }
        },
        { id: 's1_02', text: 'hobby', phonetic: '/ˈhɒb.i/', grade: 'S1', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🎨%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '嗜好' },
          definitions: { en: 'An activity that someone does for pleasure when they are not working.' },
          sentences: { en: 'My hobby is collecting stamps from different countries.' }
        },
        { id: 's1_03', text: 'delicious', phonetic: '/dɪˈlɪʃ.əs/', grade: 'S1', partOfSpeech: 'adjective',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🍔%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '美味的' },
          definitions: { en: 'Having a very pleasant taste or smell.' },
          sentences: { en: 'The cake you made was absolutely delicious.' }
        },
    ],
    S2: [
        { id: 's2_01', text: 'technology', phonetic: '/tekˈnɒl.ə.dʒi/', grade: 'S2', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E💻%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '科技' },
          definitions: { en: 'The study and knowledge of the practical, especially industrial, use of scientific discoveries.' },
          sentences: { en: 'Modern technology has changed the way we live and work in many incredible ways.' }
        },
        { id: 's2_02', text: 'environment', phonetic: '/ɪnˈvaɪ.rən.mənt/', grade: 'S2', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🌳%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '環境' },
          definitions: { en: 'The air, water, and land in or on which people, animals, and plants live.' },
          sentences: { en: 'We need to protect the environment for our future generations.' }
        },
        { id: 's2_03', text: 'communicate', phonetic: '/kəˈmjuː.nɪ.keɪt/', grade: 'S2', partOfSpeech: 'verb',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E💬%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '溝通' },
          definitions: { en: 'To share information with others by speaking, writing, moving your body, or using other signals.' },
          sentences: { en: 'We can communicate with friends overseas using the internet.' }
        },
    ],
    S3: [
        { id: 's3_01', text: 'government', phonetic: '/ˈɡʌv.ən.mənt/', grade: 'S3', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🏛️%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '政府' },
          definitions: { en: 'The group of people who officially control a country.' },
          sentences: { en: 'The government has announced a new policy on education.' }
        },
        { id: 's3_02', text: 'responsibility', phonetic: '/rɪˌspɒn.sɪˈbɪl.ə.ti/', grade: 'S3', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🤝%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '責任' },
          definitions: { en: 'Something that it is your job or duty to deal with.' },
          sentences: { en: 'It is our responsibility to take care of our parents when they are old.' }
        },
        { id: 's3_03', text: 'global', phonetic: '/ˈɡləʊ.bəl/', grade: 'S3', partOfSpeech: 'adjective',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🌍%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '全球的' },
          definitions: { en: 'Relating to the whole world.' },
          sentences: { en: 'Climate change is a global problem that affects every country.' }
        },
    ],
    TEST: [
        { id: 'test_01', text: 'Antidisestablishmentarianism', phonetic: '/ˌæntidɪsɪˌstæblɪʃmənˈtɛəriənɪzəm/', grade: 'Test', partOfSpeech: 'noun',
          imageUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext x='50%25' y='50%25' font-size='50' text-anchor='middle' dy='.3em'%3E🧐%3C/text%3E%3C/svg%3E",
          translations: { zh_TW: '反對國教廢除論。這是一個著名的長單詞，指的是19世紀英國反對廢除英國國教作為國家官方教會地位的政治運動。' },
          definitions: { en: 'Opposition to the disestablishment of the Church of England. A political movement in 19th-century Britain that opposed proposals to remove the Anglican Church\'s status as the state church.' },
          sentences: { en: 'The political debate over antidisestablishmentarianism was a defining feature of that particular historical era in the United Kingdom.' }
        }
    ]
};

// --- APPLICATION STATE ---
const appState = {
    currentMode: 'start',
    currentGrade: null,
    wordList: [],
    currentWordIndex: 0,
    practiceQuestions: [],
    currentQuestionIndex: 0,
    score: 0,
};

// --- DOM ELEMENTS ---
const startScreen = document.getElementById('start-screen');
const mainApp = document.getElementById('main-app');
const studyView = document.getElementById('study-view');
const practiceView = document.getElementById('practice-view');
const resultsView = document.getElementById('results-view');
const gradeDisplay = document.getElementById('grade-display');
const progressDisplay = document.getElementById('progress-display');
const cardWord = document.getElementById('card-word');
const cardPhonetic = document.getElementById('card-phonetic');
const cardImageContainer = document.getElementById('card-image-container');
const cardTranslationZh = document.getElementById('card-translation-zh');
const cardDefinitionEn = document.getElementById('card-definition-en');
const cardSentenceEn = document.getElementById('card-sentence-en');
const prevBtn = document.getElementById('prev-btn');
const nextBtn = document.getElementById('next-btn');
const practiceInstruction = document.getElementById('practice-instruction');
const practiceQuestion = document.getElementById('practice-question');
const practiceContent = document.getElementById('practice-content');


// --- FUNCTIONS ---

function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

function startApp(grade) {
    appState.currentGrade = grade;
    appState.wordList = shuffleArray([...wordDatabase[grade]]);
    appState.currentWordIndex = 0;
    appState.score = 0;
    
    updateView('study');
    displayCard();
}

function updateView(mode) {
    appState.currentMode = mode;
    startScreen.classList.add('hidden');
    mainApp.classList.remove('hidden');
    mainApp.classList.add('flex');

    [studyView, practiceView, resultsView].forEach(v => v.classList.add('hidden'));

    if (mode === 'study') studyView.classList.remove('hidden');
    if (mode === 'practice') practiceView.classList.remove('hidden');
    if (mode === 'results') resultsView.classList.remove('hidden');
}

function showStartScreen() {
    mainApp.classList.add('hidden');
    mainApp.classList.remove('flex');
    startScreen.classList.remove('hidden');
}

function adjustFontSize(element) {
    // Reset font size to default before calculating
    element.style.fontSize = '';
    const initialFontSize = parseFloat(window.getComputedStyle(element).fontSize);
    let currentFontSize = initialFontSize;

    // Check for overflow
    while (element.scrollWidth > element.clientWidth && currentFontSize > 10) {
        currentFontSize -= 1;
        element.style.fontSize = `${currentFontSize}px`;
    }
}

function displayCard() {
    const word = appState.wordList[appState.currentWordIndex];
    
    gradeDisplay.textContent = word.grade;
    progressDisplay.textContent = `${appState.currentWordIndex + 1} / ${appState.wordList.length}`;
    
    cardWord.textContent = word.text;
    adjustFontSize(cardWord); // Adjust font size for long words

    cardPhonetic.textContent = word.phonetic;
    cardImageContainer.innerHTML = `<img src="${word.imageUrl}" alt="${word.text}" class="w-full h-full object-contain">`;
    cardTranslationZh.textContent = word.translations.zh_TW;
    cardDefinitionEn.textContent = word.definitions.en;
    cardSentenceEn.textContent = word.sentences.en;

    // Update button states
    prevBtn.disabled = appState.currentWordIndex === 0;
    nextBtn.disabled = appState.currentWordIndex === appState.wordList.length - 1;
}

function speakWord() {
    const word = appState.wordList[appState.currentWordIndex];
    if (word && 'speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(word.text);
        utterance.lang = 'en-US';
        window.speechSynthesis.speak(utterance);
    }
}

function previousCard() {
    if (appState.currentWordIndex > 0) {
        appState.currentWordIndex--;
        displayCard();
    }
}

function nextCard() {
    if (appState.currentWordIndex < appState.wordList.length - 1) {
        appState.currentWordIndex++;
        displayCard();
    }
}

function startPractice() {
    appState.practiceQuestions = generatePracticeQuestions();
    appState.currentQuestionIndex = 0;
    appState.score = 0;
    updateView('practice');
    displayPracticeQuestion();
}

function generatePracticeQuestions() {
    let questions = [];
    const allWordsForOptions = Object.values(wordDatabase).flat();

    for (const word of appState.wordList) {
        // 1. Multiple Choice Question
        const wrongOptions = allWordsForOptions.filter(w => w.id !== word.id).sort(() => 0.5 - Math.random()).slice(0, 3);
        questions.push({
            type: 'multiple-choice',
            instruction: '根據解釋選擇正確的單詞：',
            questionText: `"${word.definitions.en}"`,
            options: shuffleArray([word, ...wrongOptions]),
            correctAnswer: word.text
        });

        // 2. Typing Question
        questions.push({
            type: 'typing',
            instruction: '根據解釋拼寫出正確的單詞：',
            questionText: `"${word.definitions.en}"`,
            correctAnswer: word.text
        });

        // 3. Fill-in-the-blank Question
        questions.push({
            type: 'fill-in-the-blank',
            instruction: '在空格中填入正確的單詞：',
            questionText: word.sentences.en.replace(new RegExp(word.text.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi'), '______'),
            correctAnswer: word.text
        });
    }
    return shuffleArray(questions);
}

function displayPracticeQuestion() {
    if (appState.currentQuestionIndex >= appState.practiceQuestions.length) {
        showResults();
        return;
    }

    const question = appState.practiceQuestions[appState.currentQuestionIndex];
    practiceInstruction.textContent = question.instruction;
    practiceQuestion.innerHTML = question.questionText; // Use innerHTML to render the blank
    practiceContent.innerHTML = '';

    switch (question.type) {
        case 'multiple-choice':
            question.options.forEach(option => {
                const button = document.createElement('button');
                button.className = "w-full bg-white border border-slate-300 hover:bg-slate-100 text-slate-800 font-medium py-3 px-4 rounded-lg";
                button.textContent = option.text;
                button.onclick = () => checkMultipleChoiceAnswer(option.text, question.correctAnswer, button);
                practiceContent.appendChild(button);
            });
            break;
        case 'typing':
        case 'fill-in-the-blank':
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'w-full border border-slate-300 rounded-lg p-3 text-center text-lg';
            input.placeholder = '在這裡輸入答案';
            input.onkeydown = (event) => {
                if (event.key === 'Enter') {
                    checkTypingAnswer(input, question.correctAnswer);
                }
            };
            practiceContent.appendChild(input);
            input.focus(); // Auto-focus the input field
            
            const submitBtn = document.createElement('button');
            submitBtn.textContent = '確定';
            submitBtn.className = 'w-full mt-3 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 rounded-lg';
            submitBtn.onclick = () => checkTypingAnswer(input, question.correctAnswer);
            practiceContent.appendChild(submitBtn);
            break;
    }
}

function checkMultipleChoiceAnswer(selectedAnswer, correctAnswer, button) {
    const buttons = practiceContent.querySelectorAll('button');
    buttons.forEach(btn => btn.disabled = true);

    if (selectedAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        appState.score++;
        button.classList.add('correct-answer');
    } else {
        button.classList.add('wrong-answer');
        buttons.forEach(btn => {
            if (btn.textContent.toLowerCase() === correctAnswer.toLowerCase()) {
                btn.classList.add('correct-answer');
            }
        });
    }
    proceedToNextQuestion();
}

function checkTypingAnswer(inputElement, correctAnswer) {
    const userAnswer = inputElement.value.trim();
    inputElement.disabled = true;
    practiceContent.querySelector('button').disabled = true;

    if (userAnswer.toLowerCase() === correctAnswer.toLowerCase()) {
        appState.score++;
        inputElement.classList.add('correct-answer');
    } else {
        inputElement.classList.add('wrong-answer');
        inputElement.value = `答案: ${correctAnswer}`;
    }
    proceedToNextQuestion();
}

function proceedToNextQuestion() {
    setTimeout(() => {
        appState.currentQuestionIndex++;
        displayPracticeQuestion();
    }, 1500);
}


function showResults() {
    updateView('results');
    const total = appState.practiceQuestions.length;
    const percentage = total > 0 ? Math.round((appState.score / total) * 100) : 0;
    document.getElementById('score-display').textContent = `${percentage}%`;

    try {
        localStorage.setItem(`score_${appState.currentGrade}`, percentage);
    } catch (e) {
        console.error("無法存取 localStorage:", e);
    }
}

</script>
</body>
</html>
