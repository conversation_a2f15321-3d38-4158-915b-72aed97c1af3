# 英語詞彙學習工具 v2 - 專案規格文檔

## 📋 專案概述

### 專案名稱
English Vocabulary Tools v2 - 香港初中生英語詞彙學習系統

### 專案目標
開發一個前後端分離的英語詞彙學習系統，支援個人化學習、教師管理和進度追蹤。

### 核心特色
- 個人化詞彙列表（每個學生有自己的生字清單）
- 雙入口系統（學生端 + 教師端）
- 支援圖片和多媒體內容
- ZIP 文件批量上傳功能
- SQLite 數據庫存儲，未來可遷移至 PostgreSQL

## 🏗️ 系統架構

### 整體架構
```
Frontend (Student Portal) ←→ Backend API ←→ SQLite Database
Frontend (Teacher Portal) ←→ Backend API ←→ File Storage
                    ↓
            ZIP Upload Processing
```

### 技術棧
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), TailwindCSS
- **Backend**: Node.js, Express.js
- **Database**: SQLite3 (初期) → PostgreSQL (未來)
- **File Processing**: Multer, ADM-ZIP
- **Authentication**: JWT 或 Session-based

## 📊 數據庫設計

### 核心表結構

#### 1. users 表（用戶管理）
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('student', 'teacher')),
    full_name TEXT NOT NULL,
    grade TEXT,                         -- 學生年級 (S1, S2, S3)
    class_id TEXT,                      -- 班級編號
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME
);
```

#### 2. vocabularies 表（詞彙主表）
```sql
CREATE TABLE vocabularies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word_id TEXT UNIQUE NOT NULL,       -- 詞彙唯一標識
    text TEXT NOT NULL,                 -- 英文單詞
    phonetic TEXT,                      -- 國際音標
    part_of_speech TEXT,                -- 詞性
    definition_en TEXT,                 -- 英文定義
    translation_zh_tw TEXT,             -- 繁體中文翻譯
    sentence_en TEXT,                   -- 英文例句
    image_path TEXT,                    -- 圖片文件路徑
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. user_vocabularies 表（用戶詞彙關聯）
```sql
CREATE TABLE user_vocabularies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    assigned_by INTEGER,                -- 分配者（教師ID）
    status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'learning', 'mastered', 'review')),
    progress INTEGER DEFAULT 0,         -- 學習進度 (0-100)
    last_practiced DATETIME,
    practice_count INTEGER DEFAULT 0,
    correct_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id),
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, vocabulary_id)
);
```

#### 4. practice_records 表（練習記錄）
```sql
CREATE TABLE practice_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    practice_type TEXT NOT NULL CHECK (practice_type IN ('multiple_choice', 'typing', 'fill_blank', 'pronunciation')),
    is_correct BOOLEAN NOT NULL,
    response TEXT,                      -- 學生回答
    time_spent INTEGER,                 -- 花費時間（秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id)
);
```

## 📁 文件結構

```
English_Vocab_Tools_v2/
├── README.md
├── PROJECT_SPECIFICATION.md           # 本文檔
├── frontend/
│   ├── student/
│   │   ├── index.html                 # 學生登入頁
│   │   ├── dashboard.html             # 學生主頁
│   │   ├── study.html                 # 學習頁面
│   │   ├── practice.html              # 練習頁面
│   │   └── progress.html              # 進度頁面
│   ├── teacher/
│   │   ├── index.html                 # 教師登入頁
│   │   ├── dashboard.html             # 教師主頁
│   │   ├── students.html              # 學生管理
│   │   ├── upload.html                # 內容上傳
│   │   └── reports.html               # 進度報告
│   ├── shared/
│   │   ├── css/
│   │   │   └── styles.css
│   │   ├── js/
│   │   │   ├── api.js                 # API 調用封裝
│   │   │   ├── auth.js                # 身份驗證
│   │   │   └── utils.js               # 工具函數
│   │   └── assets/
│   │       └── images/
├── backend/
│   ├── server.js                      # 主服務器文件
│   ├── config/
│   │   └── database.js                # 數據庫配置
│   ├── routes/
│   │   ├── auth.js                    # 身份驗證路由
│   │   ├── students.js                # 學生相關 API
│   │   ├── teachers.js                # 教師相關 API
│   │   ├── vocabularies.js            # 詞彙管理 API
│   │   └── upload.js                  # 文件上傳 API
│   ├── models/
│   │   ├── User.js                    # 用戶模型
│   │   ├── Vocabulary.js              # 詞彙模型
│   │   └── PracticeRecord.js          # 練習記錄模型
│   ├── middleware/
│   │   ├── auth.js                    # 身份驗證中間件
│   │   └── validation.js              # 數據驗證中間件
│   ├── services/
│   │   ├── uploadService.js           # 文件上傳服務
│   │   └── progressService.js         # 進度計算服務
│   └── database/
│       ├── init.sql                   # 數據庫初始化腳本
│       └── vocab.db                   # SQLite 數據庫文件
├── uploads/
│   ├── images/                        # 詞彙圖片存儲
│   └── temp/                          # 臨時文件
└── docs/
    ├── API.md                         # API 文檔
    ├── DATABASE.md                    # 數據庫文檔
    └── DEPLOYMENT.md                  # 部署文檔
```

## 🎯 功能需求

### Student Portal（學生端）
1. **用戶登入**
   - 用戶名/密碼登入
   - 記住登入狀態

2. **學習功能**
   - 查看個人詞彙列表
   - 詞彙卡片學習（單詞、音標、定義、例句、圖片）
   - 語音播放功能
   - 學習進度追蹤

3. **練習功能**
   - 多選題練習
   - 填空題練習
   - 拼寫練習
   - 即時反饋和評分

4. **進度管理**
   - 學習統計
   - 錯誤詞彙複習
   - 成就系統

### Teacher Portal（教師端）
1. **用戶管理**
   - 學生帳戶管理
   - 班級管理
   - 權限控制

2. **內容管理**
   - ZIP 文件上傳（包含 JSON + 圖片）
   - 詞彙編輯和管理
   - 批量分配詞彙給學生

3. **進度監控**
   - 學生學習進度查看
   - 班級統計報告
   - 個別學生詳細報告

4. **系統管理**
   - 數據備份
   - 系統設置

## 📤 上傳功能設計

### ZIP 文件結構
```
upload.zip
├── data.json                          # 詞彙數據
└── images/                            # 圖片文件夾
    ├── festival.jpg
    ├── hobby.png
    └── technology.gif
```

### JSON 數據格式
```json
{
  "metadata": {
    "version": "1.0.0",
    "upload_date": "2025-01-31",
    "description": "S1 詞彙包"
  },
  "vocabularies": [
    {
      "word_id": "s1_001",
      "text": "festival",
      "phonetic": "/ˈfes.tɪ.vəl/",
      "part_of_speech": "noun",
      "definition_en": "A special day or period, usually in memory of a religious event, with its own social activities, food, or ceremonies.",
      "translation_zh_tw": "節日",
      "sentence_en": "Chinese New Year is the most important festival for Chinese people.",
      "image_filename": "festival.jpg"
    }
  ]
}
```

## 🔧 API 設計

### 身份驗證 API
```
POST /api/auth/login          # 用戶登入
POST /api/auth/logout         # 用戶登出
GET  /api/auth/profile        # 獲取用戶資料
```

### 學生端 API
```
GET  /api/student/vocabularies           # 獲取學生的詞彙列表
GET  /api/student/vocabulary/:id         # 獲取特定詞彙詳情
POST /api/student/practice               # 提交練習結果
GET  /api/student/progress               # 獲取學習進度
GET  /api/student/statistics             # 獲取學習統計
```

### 教師端 API
```
GET  /api/teacher/students               # 獲取所有學生
GET  /api/teacher/student/:id/progress   # 獲取學生進度
POST /api/teacher/assign-vocabulary      # 分配詞彙給學生
POST /api/teacher/upload                 # 上傳 ZIP 文件
GET  /api/teacher/reports                # 獲取班級報告
```

### 詞彙管理 API
```
GET    /api/vocabularies                 # 獲取所有詞彙
POST   /api/vocabularies                 # 創建新詞彙
PUT    /api/vocabularies/:id             # 更新詞彙
DELETE /api/vocabularies/:id             # 刪除詞彙
GET    /api/images/:filename             # 獲取圖片文件
```

## 🔐 安全需求

### 身份驗證
- JWT Token 或 Session-based 驗證
- 密碼加密存儲（bcrypt）
- 登入狀態管理

### 權限控制
- 學生只能訪問自己的數據
- 教師可以管理分配給自己的學生
- 管理員擁有完整權限

### 文件安全
- 文件類型驗證
- 文件大小限制（50MB）
- 惡意文件檢測
- 安全的文件存儲路徑

### 數據驗證
- 輸入數據格式驗證
- SQL 注入防護
- XSS 攻擊防護

## 📱 用戶界面設計

### 設計原則
- 響應式設計（支援手機和桌面）
- 簡潔直觀的用戶界面
- 無障礙設計考慮
- 中文界面為主

### 學生端界面
1. **登入頁面**: 簡單的用戶名密碼輸入
2. **主頁面**: 顯示學習進度和快速操作
3. **學習頁面**: 詞彙卡片展示
4. **練習頁面**: 各種練習模式
5. **進度頁面**: 學習統計和成就

### 教師端界面
1. **登入頁面**: 教師身份驗證
2. **儀表板**: 班級概況和快速操作
3. **學生管理**: 學生列表和進度查看
4. **內容上傳**: ZIP 文件上傳界面
5. **報告頁面**: 詳細的學習報告

## 🚀 開發階段規劃

### 第一階段：基礎架構（2-3週）
- [ ] 數據庫設計和初始化
- [ ] 基本的 Backend API 框架
- [ ] 簡單的前端登入界面
- [ ] 基本的身份驗證系統

### 第二階段：核心功能（3-4週）
- [ ] 學生端詞彙學習功能
- [ ] 基本的練習系統
- [ ] 教師端學生管理
- [ ] 進度追蹤功能

### 第三階段：高級功能（2-3週）
- [ ] ZIP 文件上傳處理
- [ ] 圖片管理系統
- [ ] 詳細的統計報告
- [ ] 用戶界面優化

### 第四階段：測試和部署（1-2週）
- [ ] 功能測試
- [ ] 性能優化
- [ ] 安全加固
- [ ] 部署準備

## 📊 技術難度評估

| 模塊 | 難度 (1-10) | 預估時間 | 關鍵技術 |
|------|-------------|----------|----------|
| 數據庫設計 | 4/10 | 3-5天 | SQLite, SQL |
| Backend API | 7/10 | 2-3週 | Node.js, Express |
| 身份驗證 | 6/10 | 1週 | JWT, bcrypt |
| 文件上傳 | 7/10 | 1週 | Multer, ZIP處理 |
| 前端界面 | 5/10 | 2-3週 | HTML/CSS/JS |
| 整合測試 | 6/10 | 1週 | 系統整合 |

**總體難度**: 6.5/10
**預估開發時間**: 8-12週（全職開發）

## 🎯 成功指標

### 功能指標
- [ ] 支援至少 100 個學生同時使用
- [ ] 詞彙數據庫包含至少 500 個詞彙
- [ ] 系統響應時間 < 2秒
- [ ] 支援 ZIP 文件批量上傳

### 用戶體驗指標
- [ ] 學生能在 5 分鐘內完成註冊和開始學習
- [ ] 教師能在 10 分鐘內完成內容上傳
- [ ] 界面在手機和桌面都能正常使用

### 技術指標
- [ ] 代碼覆蓋率 > 80%
- [ ] 數據庫查詢優化
- [ ] 安全漏洞掃描通過
- [ ] 支援未來遷移到 PostgreSQL

## 📚 參考資料

### 現有實現
- 參考文件: `reference/index.html`
- 現有詞彙結構和練習模式

### 技術文檔
- Node.js 官方文檔
- Express.js 指南
- SQLite 文檔
- JWT 認證最佳實踐

### 設計參考
- Material Design 指南
- 教育應用 UX 最佳實踐
- 響應式設計原則

---

**文檔版本**: 1.0.0
**最後更新**: 2025-01-31
**維護者**: 開發團隊
